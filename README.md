# MCP Proxy Server for LM Studio

This proxy server enables your LM Studio llava-1.6-mistral-7b model to access MCP servers (specifically the perplexity-ask server for real-time search) even though the model doesn't natively support tools.

## How it works

1. The proxy server sits between your client and LM Studio
2. It intercepts chat requests and detects search-related queries
3. When a search is detected, it queries the Perplexity MCP server
4. The search results are injected into the conversation before forwarding to LM Studio
5. Your model receives the real-time search data and can provide informed responses

## Setup

1. Install dependencies:
```bash
npm install
```

2. Start the proxy server:
```bash
npm start
```

The proxy will run on `http://localhost:3001` and automatically connect to:
- Your LM Studio instance at `http://localhost:1234`
- The Perplexity MCP server using your configured API key

## Usage

### Option 1: Use the proxy endpoint directly
Instead of connecting to `http://localhost:1234`, connect your client to `http://localhost:3001`.

The proxy automatically detects search requests in messages containing words like:
- "search"
- "find" 
- "look up"
- "what is"
- "tell me about"
- "research"

### Option 2: Use the direct search endpoint
You can also make direct search requests:

```bash
curl -X POST http://localhost:3001/search \
  -H "Content-Type: application/json" \
  -d '{"query": "latest news about AI"}'
```

## Example

**Without proxy (normal LM Studio):**
```
User: "What's the latest news about OpenAI?"
Model: "I don't have access to real-time information..."
```

**With proxy:**
```
User: "Search for the latest news about OpenAI"
Model: "Based on the latest search results, here's what's happening with OpenAI..." 
[Includes real-time information from Perplexity]
```

## Configuration

The proxy uses the Perplexity API key from your `my_mcp_servers.json` configuration:
- API Key: `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`

## Troubleshooting

1. **Check if LM Studio is running**: Make sure your llava model is loaded and running on port 1234
2. **Check MCP server**: The proxy will show MCP server logs in the console
3. **Test the proxy**: Visit `http://localhost:3001/health` to check if everything is connected

## Health Check

```bash
curl http://localhost:3001/health
```

Should return:
```json
{"status": "ok", "mcp_connected": true}
```
