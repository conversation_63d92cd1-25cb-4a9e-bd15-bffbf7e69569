#!/usr/bin/env node

const express = require('express');
const { spawn } = require('child_process');
const cors = require('cors');
const axios = require('axios');

class MCPProxyServer {
  constructor() {
    this.app = express();
    this.mcpProcess = null;
    this.setupMiddleware();
    this.setupRoutes();
    this.startMCPServer();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ status: 'ok', mcp_connected: !!this.mcpProcess });
    });

    // Main proxy endpoint that mimics OpenAI API
    this.app.post('/v1/chat/completions', async (req, res) => {
      try {
        await this.handleChatCompletion(req, res);
      } catch (error) {
        console.error('Error handling chat completion:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Direct search endpoint
    this.app.post('/search', async (req, res) => {
      try {
        const { query } = req.body;
        if (!query) {
          return res.status(400).json({ error: 'Query is required' });
        }
        
        const result = await this.performSearch(query);
        res.json(result);
      } catch (error) {
        console.error('Search error:', error);
        res.status(500).json({ error: 'Search failed' });
      }
    });
  }

  async startMCPServer() {
    console.log('Starting Perplexity MCP server...');
    
    this.mcpProcess = spawn('npx', ['-y', 'server-perplexity-ask'], {
      env: {
        ...process.env,
        PERPLEXITY_API_KEY: '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'
      },
      stdio: ['pipe', 'pipe', 'pipe']
    });

    this.mcpProcess.stdout.on('data', (data) => {
      console.log('MCP stdout:', data.toString());
    });

    this.mcpProcess.stderr.on('data', (data) => {
      console.error('MCP stderr:', data.toString());
    });

    this.mcpProcess.on('close', (code) => {
      console.log(`MCP process exited with code ${code}`);
      this.mcpProcess = null;
    });

    // Wait a bit for the MCP server to start
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  async performSearch(query) {
    if (!this.mcpProcess) {
      throw new Error('MCP server not running');
    }

    // Send search request to MCP server with correct format
    const request = {
      jsonrpc: "2.0",
      id: Date.now(),
      method: "tools/call",
      params: {
        name: "perplexity_ask",
        arguments: {
          messages: [
            {
              role: "user",
              content: query
            }
          ]
        }
      }
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Search timeout'));
      }, 30000);

      let responseData = '';

      const dataHandler = (data) => {
        responseData += data.toString();
        try {
          const lines = responseData.split('\n').filter(line => line.trim());
          for (const line of lines) {
            const response = JSON.parse(line);
            if (response.id === request.id) {
              clearTimeout(timeout);
              this.mcpProcess.stdout.removeListener('data', dataHandler);

              if (response.error) {
                reject(new Error(response.error.message || 'Search failed'));
              } else {
                resolve(response.result);
              }
              return;
            }
          }
        } catch (e) {
          // Continue waiting for complete response
        }
      };

      this.mcpProcess.stdout.on('data', dataHandler);
      this.mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async handleChatCompletion(req, res) {
    const { messages, model, stream = false } = req.body;
    
    // Check if the last message contains a search request
    const lastMessage = messages[messages.length - 1];
    const searchPattern = /(?:search|find|look up|what is|tell me about|research)\s+(.+)/i;
    const match = lastMessage.content.match(searchPattern);

    if (match) {
      console.log('Detected search request:', match[1]);
      
      try {
        // Perform search using MCP
        const searchResult = await this.performSearch(match[1]);
        
        // Forward to LM Studio with search results
        const enhancedMessages = [
          ...messages.slice(0, -1),
          {
            role: 'user',
            content: `${lastMessage.content}\n\nSearch Results:\n${JSON.stringify(searchResult, null, 2)}`
          }
        ];

        const response = await axios.post('http://localhost:1234/v1/chat/completions', {
          ...req.body,
          messages: enhancedMessages
        });

        res.json(response.data);
      } catch (error) {
        console.error('Search failed:', error);
        // Fall back to normal LM Studio request
        const response = await axios.post('http://localhost:1234/v1/chat/completions', req.body);
        res.json(response.data);
      }
    } else {
      // Forward directly to LM Studio
      const response = await axios.post('http://localhost:1234/v1/chat/completions', req.body);
      res.json(response.data);
    }
  }

  start(port = 3001) {
    this.app.listen(port, () => {
      console.log(`MCP Proxy Server running on port ${port}`);
      console.log(`Use this endpoint instead of localhost:1234: http://localhost:${port}`);
      console.log('The proxy will automatically detect search requests and enhance them with real-time data');
    });
  }

  stop() {
    if (this.mcpProcess) {
      this.mcpProcess.kill();
    }
  }
}

// Start the server
const server = new MCPProxyServer();
server.start();

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down...');
  server.stop();
  process.exit(0);
});
