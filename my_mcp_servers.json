{
      "github": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"
      }
    },
      "Context7": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    },
      "perplexity-ask": {
        "command": "npx",
        "args": [
          "-y",
          "server-perplexity-ask"
        ],
        "env": {
          "PERPLEXITY_API_KEY": "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"
        }
      },
      "The21stDevMagic": {
        "args": [
          "-y",
          "@21st-dev/magic@latest",
          "--api-key",
          "77f99f24b81696d73870b6378c626ef9753fa56334f66261847aa6facbb3f357"
        ],
        "command": "npx"
      },
      "apify-actors": {
        "args": [
          "-y",
          "@apify/actors-mcp-server",
          "--actors",
          "lukaskrivka/google-maps-with-contact-details,apify/instagram-scraper"
        ],
        "command": "npx",
        "env": {
          "APIFY_TOKEN": "**********************************************"
        }
      },
      "brave-search": {
        "args": [
          "-y",
          "@modelcontextprotocol/server-brave-search"
        ],
        "command": "npx",
        "env": {
          "BRAVE_API_KEY": "BSAIiGF-k5xVMKf7_Pl791FvvEdJyCY"
        }
      },
      "firecrawl-mcp": {
        "args": [
          "-y",
          "firecrawl-mcp"
        ],
        "command": "npx",
        "env": {
          "FIRECRAWL_API_KEY": "fc-2cd161292bbf455da0de03784e7723fd"
        }
      },
      "heroku": {
        "args": [
          "-y",
          "@heroku/mcp-server"
        ],
        "command": "npx",
        "env": {
          "HEROKU_API_KEY": "HRKU-************************************"
        }
      },
      "mongodb": {
        "args": [
          "-y",
          "mongodb-lens@latest",
          "mongodb+srv://hajirufai:<EMAIL>/"
        ],
        "command": "npx"
      },
      "neon": {
        "args": [
          "-y",
          "@neondatabase/mcp-server-neon",
          "start",
          "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
        ],
        "command": "npx"
      },
      "tavily-mcp": {
        "args": [
          "-y",
          "tavily-mcp@0.1.4"
        ],
        "autoApprove": [],
        "command": "npx",
        "disabled": false,
        "env": {
          "TAVILY_API_KEY": "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"
        }
      },
      "shrimp-task-manager": {
        "command": "npx",
        "args": [
          "-y",
          "mcp-shrimp-task-manager"
        ],
        "env": {
          "DATA_DIR": "/Users/<USER>/mcp-shrimp-task-manager-storage-data" // 必須使用絕對路徑
        }
      }
    }
