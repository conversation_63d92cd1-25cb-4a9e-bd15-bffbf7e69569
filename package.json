{"name": "mcp-proxy-server", "version": "1.0.0", "description": "Proxy server to connect LM Studio models with MCP servers", "main": "mcp-proxy-server.js", "scripts": {"start": "node mcp-proxy-server.js", "dev": "nodemon mcp-proxy-server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["mcp", "proxy", "lm-studio", "perplexity"], "author": "", "license": "MIT"}