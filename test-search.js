#!/usr/bin/env node

const axios = require('axios');

async function testSearch() {
  console.log('Testing MCP Proxy Server...\n');

  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('Health check:', healthResponse.data);
    
    if (!healthResponse.data.mcp_connected) {
      console.log('⚠️  MCP server not connected, but proxy is running');
    }

    // Test direct search
    console.log('\n2. Testing direct search...');
    const searchResponse = await axios.post('http://localhost:3001/search', {
      query: 'latest AI news today'
    });
    console.log('Search result:', JSON.stringify(searchResponse.data, null, 2));

    // Test chat completion with search
    console.log('\n3. Testing chat completion with search...');
    const chatResponse = await axios.post('http://localhost:3001/v1/chat/completions', {
      model: 'llava-1.6-mistral-7b',
      messages: [
        {
          role: 'user',
          content: 'Search for the latest developments in AI technology'
        }
      ],
      max_tokens: 500
    });
    
    console.log('Chat response:', JSON.stringify(chatResponse.data, null, 2));

  } catch (error) {
    console.error('Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure to start the proxy server first:');
      console.log('   npm start');
    }
  }
}

// Run the test
testSearch();
